package aseclient

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"maps"
	"net/http"
	"net/url"
	"time"

	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bcost"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/tools/bif"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	boot_gzip "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/gzip"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/match"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/match/code_match"
	asemetrics "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/ase_sdk/metrics"
	asemodel "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/ase_sdk/model"
	aseproto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/ase_sdk/proto"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	"github.com/bytedance/sonic"
)

var localModelProto = pandora.NewPandoraProto[InferReq, InferResp]()

// Request 模型请求
type Request struct {
	client     *http.Client
	timeCosts  map[string]int64
	extra      map[string]any
	traceId    string
	conf       *asemodel.AseModel
	method     string
	parentSpan *span.Span
	header     map[string]string
	tlbTag     string

	reqStrategy *code_match.CodeMatch
}

func (r *Request) SetClient(c *http.Client) *Request {
	r.client = c
	return r
}

func (r *Request) SetParentSpan(span *span.Span) *Request {
	r.parentSpan = span
	return r
}

func (r *Request) SetTraceId(traceId string) *Request {
	r.traceId = traceId
	return r
}

func (r *Request) SetHeader(header map[string]string) *Request {
	r.header = header
	return r
}

func (r *Request) SetHeaderTag(tag string) *Request {
	r.tlbTag = tag
	return r
}

// prepare 方法用于准备请求的URL、请求数据、请求头和URL对象
func (r *Request) prepare(input []byte) (string, []byte, map[string]string, *url.URL, error) {
	// 组装认证URL
	authRequestUrl, err := assembleAuthUrl(r.conf.AseUrl, r.conf.API_KEY, r.conf.API_SECRET, r.method)
	if err != nil {
		return "", nil, nil, nil, err
	}

	// 解析AseUrl为URL对象
	urlResult, err := url.Parse(r.conf.AseUrl)
	if err != nil {
		return "", nil, nil, nil, err
	}

	// 生成请求头
	headers := map[string]string{
		"app_id":       r.conf.APP_ID,
		"Content-Type": "application/json",
		"host":         urlResult.Host,
	}

	// 创建ASE请求对象并设置请求数据
	request_data := aseproto.CreateAseRequest()
	request_data.Header.AppId = r.conf.APP_ID
	request_data.Parameter.ModelServer.TraceId = r.traceId

	dst, cost, err := boot_gzip.Compress(input)
	if err != nil {
		return "", nil, nil, nil, err
	}

	r.extra["gzip_len"] = len(dst)
	r.timeCosts["gzip"] = cost

	r.timeCosts["base64"] = bcost.Cost(func() {
		request_data.Payload.Data.Text = base64.StdEncoding.EncodeToString(dst)
	}).Milliseconds()

	// 将请求数据序列化为JSON格式
	st := time.Now()
	requestDataJson, err := sonic.Marshal(request_data)
	if err != nil {
		return "", nil, nil, nil, err
	}

	r.timeCosts["prepare_marshal"] = time.Now().Sub(st).Milliseconds()

	return authRequestUrl, requestDataJson, headers, urlResult, nil
}

// dealResponseData 方法用于处理响应数据
func (r *Request) dealResponseData(response *http.Response) ([]byte, error) {
	// 检查响应状态码，如果不是200则返回错误
	if response.StatusCode != 200 {
		return nil, fmt.Errorf("ase plat request error:%d", response.StatusCode)
	}

	// 读取响应体数据
	body, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, fmt.Errorf("read response body failed, err: %v", err.Error())
	}

	r.extra["resp_len"] = len(body)

	// 解析响应体为PlatformResponse对象
	var data aseproto.PlatformResponse
	if err := sonic.Unmarshal(body, &data); err != nil {
		return nil, fmt.Errorf("ase plat error, err: %s", string(body))
	}

	r.extra["sid"] = data.Header.Sid

	// 检查响应头中的错误码，如果不是0则返回错误
	if data.Header.Code != 0 {
		return nil, fmt.Errorf("engine response code is not 0, code: %d,mesg: %s", data.Header.Code, data.Header.Message)
	}

	// 检查响应数据中的文本是否为空，如果为空则返回错误
	if data.PayLoad.Data.Text == "" {
		return nil, fmt.Errorf("ase plat request error:%s", string(body))
	}

	st := time.Now()
	// 解码Base64编码的响应文本
	realData, err := base64.StdEncoding.DecodeString(data.PayLoad.Data.Text)
	if err != nil {
		return nil, fmt.Errorf("engine resp base64 decode failed, err: %v", err.Error())
	}

	r.timeCosts["decode64"] = time.Now().Sub(st).Milliseconds()

	//ungzip
	ungipData, cost, err := boot_gzip.Depress(realData)
	if err != nil {
		return nil, err
	}

	r.timeCosts["ungip"] = cost
	return ungipData, nil
}

// Send 发送ASE POST请求
func (r *Request) aseSend(ctx context.Context, input []byte) (out []byte, err error) {
	var url *url.URL
	var status_code = 200
	var auth_url = ""
	var req_data []byte
	var req_headers map[string]string

	r.timeCosts["prepare"] = bcost.Cost(func() {
		auth_url, req_data, req_headers, url, err = r.prepare(input)
	}).Milliseconds()

	if err != nil {
		return nil, err
	}

	r.extra["base64_len"] = len(req_data)
	// 创建带有上下文的 HTTP 请求
	req, err := http.NewRequestWithContext(ctx, r.method, auth_url, bytes.NewBuffer(req_data))
	if err != nil {
		return nil, err
	}

	// 设置请求头
	for key, value := range req_headers {
		req.Header.Set(key, value)
	}

	if r.header != nil {
		for key, value := range r.header {
			req.Header.Set(key, value)
		}
	}

	// 统计请求总数和并发数
	asemetrics.Global_metrics.StatsTotal(url.Path, req.Method, 1)
	asemetrics.Global_metrics.AddConcurrency(url.Path, req.Method, 1)

	// 在请求结束时，统计并发数下降、请求耗时和请求状态
	defer func() {
		asemetrics.Global_metrics.DescConcurrency(url.Path, req.Method, 1)
		asemetrics.Global_metrics.StatsStatus(url.Path, req.Method, status_code, 1)

		if v, ok := r.timeCosts["do_request"]; ok {
			asemetrics.Global_metrics.StatsElapse(url.Path, req.Method, int(v))
		}
	}()

	// 发送 HTTP 请求
	var resp *http.Response
	r.timeCosts["do_request"] = bcost.Cost(func() {
		resp, err = r.client.Do(req)
	}).Milliseconds()

	if err != nil {
		// 如果请求失败，设置状态码为未知错误
		status_code = 10001 //未知错误
		return nil, err
	}

	// 确保响应体在函数结束时关闭
	defer resp.Body.Close()

	// 更新状态码为响应状态码
	status_code = resp.StatusCode

	// 处理响应数据并返回
	r.timeCosts["deal_response_data"] = bcost.Cost(func() {
		out, err = r.dealResponseData(resp)
	}).Milliseconds()
	return out, err
}

// ase 模式
func (r *Request) reqAse(ctx context.Context, input *InferReq) (out *pandora_proto.PandoraResponseMessage[InferResp], err error) {
	var inputBytes []byte
	//时间统计
	r.timeCosts["json_marshal"] = bcost.Cost(func() {
		inputBytes, err = sonic.Marshal(input)
	}).Milliseconds()

	if err != nil {
		return nil, err
	}

	// 发送ASE请求
	r.extra["input_len"] = len(inputBytes)
	rout, err := r.aseSend(ctx, inputBytes)
	if err != nil {
		return nil, err
	}

	// 解析ASE响应数据
	resp := pandora_proto.NewPandoraResponseMessage[InferResp]()
	r.timeCosts["json_unmarshal"] = bcost.Cost(func() {
		err = sonic.Unmarshal(rout, &resp.Payload)
	}).Milliseconds()

	if err != nil {
		return nil, err
	}

	return resp, nil
}

// tlb 模式
func (r *Request) reqTlb(ctx context.Context, input *InferReq) (out *pandora_proto.PandoraResponseMessage[InferResp], err error) {
	// 发送本地请求
	resp, err := localModelProto.Request().
		SetServerName(r.conf.TlbServer).
		SetPath("/" + r.conf.Name()).
		SetPayload(input).
		SetTraceId(r.traceId).
		SetParentSpan(r.parentSpan).
		SetHeaderTag(r.tlbTag).
		Post(ctx)

	return resp, err
}

// 本地请求模式
func (r *Request) reqIp(ctx context.Context, input *InferReq) (out *pandora_proto.PandoraResponseMessage[InferResp], err error) {
	// 发送本地请求
	resp, err := localModelProto.Request().
		SetAddr(r.conf.LocalUrl).
		SetPayload(input).
		SetTraceId(r.traceId).
		SetParentSpan(r.parentSpan).
		SetHeaderTag(r.tlbTag).
		Post(ctx)

	return resp, err
}

// SendByMode 按照指定模式请求
func (r *Request) Send(ctx context.Context, input *InferReq) (out *pandora_proto.PandoraResponseMessage[InferResp], err error) {
	st := time.Now().UnixMilli()
	var span *span.Span

	// 创建一个新的上下文，设置超时时间
	newCtx, cancel := context.WithTimeout(ctx, time.Duration(r.conf.TimeOutMills)*time.Millisecond)
	defer cancel()

	// 如果存在父span，则创建一个新的子span
	bif.Cond(r.parentSpan != nil, func() {
		span = r.parentSpan.AddSpan("model request")
	})

	// 延迟函数，用于记录请求结束时的各种指标
	defer func() {

		// 如果启用了指标统计，则记录服务器处理时间、推理时间和等待时间
		bif.Cond(asemetrics.MetricsEnable && out != nil, func() {
			asemetrics.Global_metrics.StatsServerCost(r.conf.Name(), http.MethodPost, int(out.Payload.Cost))
			asemetrics.Global_metrics.StatsInferCost(r.conf.Name(), http.MethodPost, int(out.Payload.InferCost))
			asemetrics.Global_metrics.StatsWaitCost(r.conf.Name(), http.MethodPost, int(out.Payload.WaitCost))
		})

		//记录推理耗时信息
		bif.Cond(out != nil, func() {
			r.timeCosts["ServerCost"] = out.Payload.Cost
			r.timeCosts["InferCost"] = out.Payload.InferCost
			r.timeCosts["WaitCost"] = out.Payload.WaitCost
			if out.Payload.CostExtra != nil {
				maps.Copy(r.timeCosts, out.Payload.CostExtra)
			}
		})

		bif.Cond(span != nil, func() {
			// 记录请求总耗时
			span.TraceInfo("RequestCost", time.Now().UnixMilli()-st)

			if err != nil {
				span.TraceInfo("error", err.Error())
			}

			span.TraceInfo("timeCosts", r.timeCosts)
			span.TraceInfo("extra", r.extra)
			// 结束span
			span.Finish()
		})
	}()

	//设置context
	mCtx := match.NewMatchContext(newCtx)
	mCtx.Set("input", input)
	bMatch := r.reqStrategy.Excute(mCtx, r.conf.Mode)

	if !bMatch {
		// 不支持的模式
		return nil, fmt.Errorf("unsupport mode: %d", r.conf.Mode)
	}

	if mCtx.Error() != nil {
		return nil, mCtx.Error()
	}

	return mCtx.Get("out").(*pandora_proto.PandoraResponseMessage[InferResp]), nil
}
